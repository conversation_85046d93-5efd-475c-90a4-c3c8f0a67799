#!/usr/bin/env python3
"""
Fix and Test Falcon3 Model
Comprehensive testing and fixing of the Falcon3 finance model
"""

import subprocess
import asyncio
import time
from datetime import datetime
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

console = Console()

class Falcon3ModelFixer:
    """Fix and validate the Falcon3 finance model"""
    
    def __init__(self):
        self.base_model = "falcon3:10b"
        self.trained_model = "noryon-falcon3-finance-v1:latest"
        self.test_queries = [
            "Analyze AAPL stock at $185. Provide a brief trading recommendation.",
            "What is the risk assessment for a $10,000 investment in TSLA?",
            "Provide portfolio allocation advice for a moderate risk investor."
        ]
    
    async def test_base_model(self):
        """Test if base Falcon3 model is working"""
        console.print("[yellow]🧪 Testing base Falcon3 model...[/yellow]")
        
        try:
            result = subprocess.run([
                'ollama', 'run', self.base_model, 'Hello, can you help with financial analysis?'
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0 and len(result.stdout.strip()) > 10:
                console.print("[green]✅ Base Falcon3 model is working[/green]")
                return True
            else:
                console.print(f"[red]❌ Base model failed: {result.stderr}[/red]")
                return False
                
        except subprocess.TimeoutExpired:
            console.print("[red]❌ Base model test timed out[/red]")
            return False
        except Exception as e:
            console.print(f"[red]❌ Base model error: {str(e)}[/red]")
            return False
    
    async def test_trained_model(self):
        """Test if trained Falcon3 model is working"""
        console.print("[yellow]🧪 Testing trained Falcon3 finance model...[/yellow]")
        
        try:
            result = subprocess.run([
                'ollama', 'run', self.trained_model, 'Provide a quick financial analysis of AAPL.'
            ], capture_output=True, text=True, timeout=90)
            
            if result.returncode == 0 and len(result.stdout.strip()) > 50:
                console.print("[green]✅ Trained Falcon3 model is working[/green]")
                return True, result.stdout.strip()
            else:
                console.print(f"[red]❌ Trained model failed: {result.stderr}[/red]")
                return False, None
                
        except subprocess.TimeoutExpired:
            console.print("[red]❌ Trained model test timed out[/red]")
            return False, None
        except Exception as e:
            console.print(f"[red]❌ Trained model error: {str(e)}[/red]")
            return False, None
    
    async def recreate_model_if_needed(self):
        """Recreate the Falcon3 finance model if needed"""
        console.print("[yellow]🔧 Recreating Falcon3 finance model...[/yellow]")
        
        # Create enhanced modelfile for Falcon3
        modelfile_content = """
FROM falcon3:10b

SYSTEM \"\"\"You are an elite portfolio management specialist for the Noryon AI trading system. You excel at:

ADVANCED PORTFOLIO MANAGEMENT:
- Modern Portfolio Theory implementation and optimization
- Risk-adjusted return maximization using Sharpe ratio optimization
- Dynamic asset allocation based on market conditions and volatility
- Multi-factor risk models including Fama-French factors
- Portfolio rebalancing strategies with tax-loss harvesting

INSTITUTIONAL-GRADE ANALYSIS:
- Large-scale portfolio construction for institutional investors
- Alternative investment integration (REITs, commodities, private equity)
- Currency hedging strategies for international portfolios
- ESG factor integration and sustainable investing approaches
- Performance attribution analysis and factor decomposition

SOPHISTICATED RISK MANAGEMENT:
- Value at Risk (VaR) and Conditional VaR calculations
- Stress testing and scenario analysis for portfolio resilience
- Correlation breakdown analysis during market stress
- Tail risk hedging and black swan protection strategies
- Liquidity risk assessment and management

QUANTITATIVE PORTFOLIO STRATEGIES:
- Factor investing and smart beta strategies
- Risk parity and equal risk contribution portfolios
- Long-short equity strategies and market neutral approaches
- Momentum and mean reversion factor timing
- Volatility targeting and risk budgeting frameworks

CORE FINANCIAL CAPABILITIES:
- Real-time market analysis and trend identification
- Risk assessment and portfolio optimization
- Trading signal generation with entry/exit points
- Options strategy design and derivatives analysis
- Cryptocurrency and DeFi market analysis
- Economic indicator interpretation and forecasting

NORYON TRADING SYSTEM INTEGRATION:
- Compatible with Noryon AI trading infrastructure
- Provides structured JSON responses when requested
- Integrates with risk management systems
- Supports automated trading signal generation
- Maintains audit trail for all recommendations

Always provide specific, actionable recommendations with:
- Clear entry and exit points
- Risk management parameters
- Position sizing recommendations
- Confidence levels and probability assessments
- Time horizon and monitoring requirements

Focus on: US equities, major cryptocurrencies, forex, commodities, and derivatives.
\"\"\"

PARAMETER temperature 0.3
PARAMETER top_p 0.9
PARAMETER top_k 40
PARAMETER repeat_penalty 1.1
PARAMETER num_ctx 4096
"""
        
        # Save modelfile
        with open("Modelfile.falcon3_finance", "w", encoding="utf-8") as f:
            f.write(modelfile_content)
        
        try:
            # Create the model
            result = subprocess.run([
                'ollama', 'create', 'noryon-falcon3-finance-v1', '-f', 'Modelfile.falcon3_finance'
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                console.print("[green]✅ Falcon3 finance model recreated successfully[/green]")
                return True
            else:
                console.print(f"[red]❌ Model creation failed: {result.stderr}[/red]")
                return False
                
        except subprocess.TimeoutExpired:
            console.print("[red]❌ Model creation timed out[/red]")
            return False
        except Exception as e:
            console.print(f"[red]❌ Model creation error: {str(e)}[/red]")
            return False
    
    async def comprehensive_model_test(self):
        """Run comprehensive tests on the Falcon3 model"""
        console.print("[yellow]🧪 Running comprehensive Falcon3 model tests...[/yellow]")
        
        test_results = []
        
        for i, query in enumerate(self.test_queries, 1):
            console.print(f"[cyan]  Test {i}/3: {query[:50]}...[/cyan]")
            
            try:
                result = subprocess.run([
                    'ollama', 'run', self.trained_model, query
                ], capture_output=True, text=True, timeout=90)
                
                if result.returncode == 0:
                    response = result.stdout.strip()
                    quality_score = self._calculate_quality_score(response)
                    
                    test_results.append({
                        "query": query,
                        "success": True,
                        "response_length": len(response),
                        "quality_score": quality_score,
                        "response": response[:200] + "..." if len(response) > 200 else response
                    })
                    
                    console.print(f"[green]    ✅ Test {i} passed (Quality: {quality_score:.1f}/100)[/green]")
                else:
                    test_results.append({
                        "query": query,
                        "success": False,
                        "error": result.stderr
                    })
                    console.print(f"[red]    ❌ Test {i} failed[/red]")
                    
            except subprocess.TimeoutExpired:
                test_results.append({
                    "query": query,
                    "success": False,
                    "error": "Timeout"
                })
                console.print(f"[red]    ❌ Test {i} timed out[/red]")
            except Exception as e:
                test_results.append({
                    "query": query,
                    "success": False,
                    "error": str(e)
                })
                console.print(f"[red]    ❌ Test {i} error: {str(e)}[/red]")
        
        return test_results
    
    def _calculate_quality_score(self, response):
        """Calculate response quality score"""
        score = 0
        
        # Length check
        if len(response) > 100:
            score += 25
        if len(response) > 300:
            score += 15
        
        # Financial keywords
        financial_keywords = ["analysis", "risk", "portfolio", "investment", "trading", "market", "price", "return"]
        keyword_count = sum(1 for keyword in financial_keywords if keyword.lower() in response.lower())
        score += min(keyword_count * 5, 30)
        
        # Recommendations
        if any(word in response.lower() for word in ["recommend", "suggest", "buy", "sell", "hold"]):
            score += 20
        
        # Specific numbers/targets
        if any(char in response for char in ["$", "%"]):
            score += 10
        
        return min(score, 100)
    
    async def fix_and_validate_falcon3(self):
        """Main function to fix and validate Falcon3 model"""
        console.print(Panel(
            "[bold blue]🔧 Falcon3 Model Fix & Validation[/bold blue]\n\n"
            "Steps:\n"
            "1. Test base Falcon3 model\n"
            "2. Test trained finance model\n"
            "3. Recreate if needed\n"
            "4. Run comprehensive tests\n"
            "5. Validate for production use",
            title="Falcon3 Model Fixer"
        ))
        
        start_time = datetime.now()
        
        # Step 1: Test base model
        base_working = await self.test_base_model()
        
        if not base_working:
            console.print("[red]❌ Base Falcon3 model not working. Please check Ollama installation.[/red]")
            return False
        
        # Step 2: Test trained model
        trained_working, sample_response = await self.test_trained_model()
        
        if not trained_working:
            console.print("[yellow]⚠️ Trained model not working. Recreating...[/yellow]")
            
            # Step 3: Recreate model
            recreation_success = await self.recreate_model_if_needed()
            
            if not recreation_success:
                console.print("[red]❌ Failed to recreate Falcon3 model[/red]")
                return False
            
            # Test again after recreation
            trained_working, sample_response = await self.test_trained_model()
        
        if not trained_working:
            console.print("[red]❌ Falcon3 model still not working after recreation[/red]")
            return False
        
        # Step 4: Comprehensive testing
        test_results = await self.comprehensive_model_test()
        
        # Step 5: Validation
        successful_tests = sum(1 for result in test_results if result["success"])
        success_rate = successful_tests / len(test_results) * 100
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        # Generate report
        console.print(Panel(
            f"[bold green]🎉 Falcon3 Model Fix Complete![/bold green]\n\n"
            f"Duration: {duration}\n"
            f"Tests Passed: {successful_tests}/{len(test_results)}\n"
            f"Success Rate: {success_rate:.1f}%\n\n"
            f"Model Status: {'✅ READY FOR PRODUCTION' if success_rate >= 80 else '⚠️ NEEDS ATTENTION'}\n\n"
            f"Sample Response:\n{sample_response[:200] if sample_response else 'No response'}...",
            title="Fix Results"
        ))
        
        return success_rate >= 80

async def main():
    """Main function"""
    console.print("[bold blue]🚀 Starting Falcon3 Model Fix...[/bold blue]\n")
    
    fixer = Falcon3ModelFixer()
    success = await fixer.fix_and_validate_falcon3()
    
    if success:
        console.print("\n[bold green]✅ Falcon3 model is now ready for the 10-model ensemble![/bold green]")
    else:
        console.print("\n[bold red]❌ Falcon3 model needs manual attention.[/bold red]")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
