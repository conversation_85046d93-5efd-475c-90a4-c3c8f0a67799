#!/usr/bin/env python3
"""
Simple Finance Data Trainer
Direct training on your financial datasets with minimal dependencies
"""

import os
import sys
import json
import subprocess
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

console = Console()

class SimpleFinanceTrainer:
    """Simple trainer focused on your financial data"""
    
    def __init__(self):
        self.data_dir = Path("data")
        self.models_dir = Path("models")
        self.models_dir.mkdir(exist_ok=True)
        
        # Your preferred models
        self.models = {
            "deepseek": {
                "name": "deepseek-ai/DeepSeek-R1-Distill-Llama-8B",
                "output": "models/deepseek-finance-trained",
                "specialization": "Advanced Financial Reasoning"
            },
            "mistral": {
                "name": "mistralai/Mistral-7B-Instruct-v0.2",
                "output": "models/mistral-finance-trained", 
                "specialization": "Trading Strategy Analysis"
            },
            "qwen3": {
                "name": "Qwen/Qwen2.5-7B-Instruct",
                "output": "models/qwen3-finance-trained",
                "specialization": "Market Data Analysis"
            },
            "gemma": {
                "name": "google/gemma-2-9b-it",
                "output": "models/gemma-finance-trained",
                "specialization": "Risk Assessment"
            },
            "phi4": {
                "name": "microsoft/Phi-3-medium-4k-instruct", 
                "output": "models/phi4-finance-trained",
                "specialization": "Economic Analysis"
            }
        }
    
    def check_available_data(self):
        """Check what financial data is available"""
        datasets = []
        
        # Check for key financial datasets
        key_datasets = [
            "JosephgflowersFinance-Instruct-500k",
            "BAAIIndustryInstruction_Finance-Economics", 
            "paperswithbacktestStocks-Daily-Price",
            "sp500_news_290k_articles.csv",
            "0xMakatrading-candles-subset-qa-format"
        ]
        
        for dataset in key_datasets:
            path = self.data_dir / dataset
            if path.exists():
                size = self._get_size(path)
                datasets.append({
                    "name": dataset,
                    "path": str(path),
                    "size": size,
                    "available": True
                })
            else:
                datasets.append({
                    "name": dataset,
                    "path": str(path),
                    "size": "N/A",
                    "available": False
                })
        
        return datasets
    
    def _get_size(self, path):
        """Get file/directory size"""
        try:
            if path.is_file():
                return f"{path.stat().st_size / (1024*1024):.1f} MB"
            elif path.is_dir():
                total = sum(f.stat().st_size for f in path.rglob('*') if f.is_file())
                return f"{total / (1024*1024):.1f} MB"
        except:
            pass
        return "Unknown"
    
    def display_data_status(self):
        """Display available financial data"""
        datasets = self.check_available_data()
        
        table = Table(title="📊 Financial Training Data Status")
        table.add_column("Dataset", style="cyan")
        table.add_column("Available", style="green")
        table.add_column("Size", style="yellow")
        table.add_column("Type", style="magenta")
        
        for dataset in datasets:
            status = "✅ YES" if dataset["available"] else "❌ NO"
            data_type = self._get_data_type(dataset["name"])
            table.add_row(dataset["name"], status, dataset["size"], data_type)
        
        console.print(table)
        return datasets
    
    def _get_data_type(self, name):
        """Get dataset type"""
        if "Instruct" in name or "Finance-Instruct" in name:
            return "Instructions"
        elif "news" in name.lower():
            return "News/Sentiment"
        elif "stocks" in name.lower() or "candles" in name.lower():
            return "Market Data"
        elif "economics" in name.lower():
            return "Economics"
        else:
            return "Mixed"
    
    def create_training_script(self, model_key, model_info):
        """Create a simple training script"""
        script_content = f'''#!/usr/bin/env python3
"""
Simple training script for {model_key}
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, TrainingArguments, Trainer
from datasets import load_dataset, Dataset
import json
import os
from pathlib import Path

def load_financial_data():
    """Load available financial datasets"""
    data_files = []
    
    # Try to load main financial instruction dataset
    finance_path = Path("data/JosephgflowersFinance-Instruct-500k")
    if finance_path.exists():
        for file in finance_path.rglob("*.jsonl"):
            data_files.append(str(file))
        for file in finance_path.rglob("*.json"):
            data_files.append(str(file))
    
    # Try economics dataset
    econ_path = Path("data/BAAIIndustryInstruction_Finance-Economics")
    if econ_path.exists():
        for file in econ_path.rglob("*.jsonl"):
            data_files.append(str(file))
        for file in econ_path.rglob("*.json"):
            data_files.append(str(file))
    
    if not data_files:
        print("No financial data files found")
        return None

    print(f"Found {{len(data_files)}} data files")
    
    # Load and combine data
    all_data = []
    for file_path in data_files[:5]:  # Limit to first 5 files to avoid memory issues
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                if file_path.endswith('.jsonl'):
                    for line in f:
                        if line.strip():
                            all_data.append(json.loads(line))
                else:
                    data = json.load(f)
                    if isinstance(data, list):
                        all_data.extend(data)
                    else:
                        all_data.append(data)
            print(f"✅ Loaded {{file_path}}")
        except Exception as e:
            print(f"⚠️ Could not load {{file_path}}: {{e}}")
    
    return Dataset.from_list(all_data[:10000])  # Limit to 10k examples for quick training

def main():
    print("🚀 Starting {model_key} training on financial data...")
    
    # Load model and tokenizer
    model_name = "{model_info['name']}"
    print(f"📥 Loading model: {{model_name}}")
    
    tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
        device_map="auto" if torch.cuda.is_available() else None,
        trust_remote_code=True
    )
    
    # Load financial data
    dataset = load_financial_data()
    if dataset is None:
        print("❌ No data available for training")
        return
    
    print(f"📊 Dataset size: {{len(dataset)}} examples")
    
    # Prepare data for training
    def format_data(example):
        # Handle different data formats
        if 'instruction' in example and 'output' in example:
            text = f"### Instruction: {{example['instruction']}}\\n### Response: {{example['output']}}"
        elif 'question' in example and 'answer' in example:
            text = f"### Question: {{example['question']}}\\n### Answer: {{example['answer']}}"
        elif 'text' in example:
            text = example['text']
        else:
            text = str(example)
        
        return {{"text": text}}
    
    dataset = dataset.map(format_data)
    
    # Tokenize
    def tokenize_function(examples):
        return tokenizer(
            examples["text"],
            truncation=True,
            padding=True,
            max_length=1024,  # Shorter for stability
            return_tensors="pt"
        )
    
    tokenized_dataset = dataset.map(tokenize_function, batched=True)
    
    # Training arguments - conservative settings
    training_args = TrainingArguments(
        output_dir="{model_info['output']}",
        num_train_epochs=1,  # Just 1 epoch for quick training
        per_device_train_batch_size=1,  # Small batch size
        gradient_accumulation_steps=4,
        learning_rate=5e-5,  # Conservative learning rate
        weight_decay=0.01,
        logging_steps=10,
        save_steps=100,
        warmup_steps=50,
        fp16=torch.cuda.is_available(),
        dataloader_pin_memory=False,
        remove_unused_columns=False,
        report_to=None,
        save_total_limit=2
    )
    
    # Simple trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=tokenized_dataset,
        tokenizer=tokenizer
    )
    
    # Train
    print("🔥 Starting training...")
    try:
        trainer.train()
        trainer.save_model()
        tokenizer.save_pretrained("{model_info['output']}")
        print("✅ Training completed successfully!")
        return True
    except Exception as e:
        print(f"❌ Training failed: {{e}}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
'''
        return script_content
    
    def train_model(self, model_key):
        """Train a single model"""
        model_info = self.models[model_key]
        
        console.print(f"[green]🚀 Training {model_key} - {model_info['specialization']}[/green]")
        
        # Create training script
        script_content = self.create_training_script(model_key, model_info)
        script_path = f"train_{model_key}_simple.py"
        
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        try:
            # Run training
            result = subprocess.run([
                sys.executable, script_path
            ], capture_output=True, text=True, timeout=1800)  # 30 min timeout
            
            success = result.returncode == 0
            
            if success:
                console.print(f"[green]✅ {model_key} training completed![/green]")
            else:
                console.print(f"[red]❌ {model_key} training failed[/red]")
                console.print(f"Error: {result.stderr[:300]}")
            
            return success
            
        except subprocess.TimeoutExpired:
            console.print(f"[yellow]⏰ {model_key} training timed out[/yellow]")
            return False
        except Exception as e:
            console.print(f"[red]❌ {model_key} training error: {e}[/red]")
            return False
        finally:
            # Clean up
            if os.path.exists(script_path):
                os.remove(script_path)
    
    def train_all(self):
        """Train all models"""
        console.print(Panel(
            "[bold blue]🏦 Simple Finance AI Training[/bold blue]\n\n"
            "Training your preferred models on financial data:\n"
            "• DeepSeek R1 (Financial Reasoning)\n"
            "• Mistral (Trading Strategies)\n"
            "• Qwen3 (Market Analysis)\n"
            "• Gemma (Risk Assessment)\n"
            "• Phi4 (Economic Analysis)",
            title="Finance Training"
        ))
        
        # Show available data
        datasets = self.display_data_status()
        available_data = sum(1 for d in datasets if d["available"])
        
        if available_data == 0:
            console.print("[red]❌ No financial data available for training[/red]")
            return
        
        console.print(f"\n[green]✅ Found {available_data} financial datasets[/green]")
        
        # Train each model
        results = {}
        for model_key in self.models:
            results[model_key] = self.train_model(model_key)
        
        # Summary
        successful = sum(results.values())
        total = len(results)
        
        console.print(Panel(
            f"[bold green]📊 Training Results[/bold green]\n\n"
            f"Successfully trained: {successful}/{total} models\n"
            f"Success rate: {successful/total*100:.1f}%\n\n"
            "Next steps:\n"
            "• Test trained models\n"
            "• Start paper trading\n"
            "• Monitor performance",
            title="Training Complete"
        ))

def main():
    trainer = SimpleFinanceTrainer()
    trainer.train_all()

if __name__ == "__main__":
    main()
