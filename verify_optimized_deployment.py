#!/usr/bin/env python3
"""
Noryon Phase 2 Optimized Deployment Verification
Verifies that all optimizations are working correctly
"""

import asyncio
import sys
import logging
from pathlib import Path

# Add the project root to the path
sys.path.append(str(Path(__file__).parent))

from core.ai.phase2_integration_system import Phase2IntegrationSystem
from core.ai.system_optimization_report import SystemOptimizationReport

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def verify_optimized_deployment():
    """Verify that the optimized Phase 2 deployment is working correctly"""
    
    print("\n" + "="*80)
    print("NORYON PHASE 2 OPTIMIZED DEPLOYMENT VERIFICATION")
    print("="*80)
    
    try:
        # Initialize the Phase 2 system
        print("\n🔄 Initializing Phase 2 Integration System...")
        phase2_system = Phase2IntegrationSystem()
        
        # Test initialization
        print("🔄 Testing system initialization...")
        init_success = await phase2_system.initialize()
        
        if init_success:
            print("✅ System initialization: SUCCESS")
            
            # Check component status
            print("\n🔍 Checking component status...")
            system_status = phase2_system.get_system_status()
            components = system_status['components']
            
            working_count = 0
            for comp_name, status in components.items():
                if status['is_active'] and status['is_healthy']:
                    print(f"✅ {comp_name}: ACTIVE & HEALTHY")
                    working_count += 1
                else:
                    print(f"❌ {comp_name}: INACTIVE or UNHEALTHY")
            
            print(f"\n📊 Working components: {working_count}/{len(components)}")
            
            # Test system metrics
            print("\n🔄 Testing system metrics...")
            metrics = phase2_system.get_system_metrics()
            print(f"✅ System state: {metrics.system_state.value}")
            print(f"✅ Active agents: {metrics.active_agents}")
            print(f"✅ Overall performance: {metrics.overall_performance:.2f}")
            
            # Test optimized features
            print("\n🔄 Testing optimized features...")
            
            # Check if optimized components are being used
            from core.ai.phase2_integration_system import OPTIMIZED_COMPONENTS_AVAILABLE
            if OPTIMIZED_COMPONENTS_AVAILABLE:
                print("✅ Optimized components: AVAILABLE & LOADED")
                
                # Test multi-agent coordination enhancements
                mac_component = phase2_system.components.get(
                    phase2_system.ComponentType.MULTI_AGENT_COORDINATION
                )
                if mac_component and hasattr(mac_component.component_instance, 'get_performance_metrics'):
                    print("✅ Multi-Agent Coordination: ENHANCED VERSION ACTIVE")
                else:
                    print("⚠️  Multi-Agent Coordination: FALLBACK VERSION ACTIVE")
                
                # Test adaptive learning enhancements
                al_component = phase2_system.components.get(
                    phase2_system.ComponentType.ADAPTIVE_LEARNING
                )
                if al_component and hasattr(al_component.component_instance, 'get_performance_metrics'):
                    print("✅ Adaptive Learning: ENHANCED VERSION ACTIVE")
                else:
                    print("⚠️  Adaptive Learning: FALLBACK VERSION ACTIVE")
            else:
                print("⚠️  Optimized components: NOT AVAILABLE (using fallbacks)")
            
            # Test system resilience
            print("\n🔄 Testing system resilience...")
            
            # Simulate component health check
            health_results = []
            for comp_type, component in phase2_system.components.items():
                health = await component.health_check()
                health_results.append(health)
                status = "HEALTHY" if health else "UNHEALTHY"
                print(f"✅ {comp_type.value} health check: {status}")
            
            overall_health = all(health_results)
            print(f"\n📊 Overall system health: {'EXCELLENT' if overall_health else 'NEEDS ATTENTION'}")
            
            # Performance summary
            print("\n" + "="*80)
            print("VERIFICATION SUMMARY")
            print("="*80)
            print(f"✅ System Initialization: {'SUCCESS' if init_success else 'FAILED'}")
            print(f"✅ Working Components: {working_count}/2 expected")
            print(f"✅ Optimized Features: {'ACTIVE' if OPTIMIZED_COMPONENTS_AVAILABLE else 'FALLBACK'}")
            print(f"✅ System Health: {'EXCELLENT' if overall_health else 'NEEDS ATTENTION'}")
            print(f"✅ Deployment Status: {'FULLY OPERATIONAL' if init_success and working_count >= 2 else 'PARTIAL'}")
            
            if init_success and working_count >= 2:
                print("\n🎉 VERIFICATION COMPLETE: ALL SYSTEMS OPERATIONAL! 🎉")
                print(f"DEBUG: init_success={init_success}, working_count={working_count}")
                return True
            else:
                print("\n⚠️  VERIFICATION INCOMPLETE: Some issues detected")
                print(f"DEBUG: init_success={init_success}, working_count={working_count}")
                return False
                
        else:
            print("❌ System initialization: FAILED")
            return False
            
    except Exception as e:
        print(f"❌ Verification failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Cleanup
        try:
            if 'phase2_system' in locals():
                await phase2_system.shutdown()
                print("\n🔄 System shutdown complete")
        except Exception as e:
            print(f"⚠️  Shutdown warning: {e}")

def main():
    """Main verification function"""
    print("Starting Noryon Phase 2 Optimized Deployment Verification...")
    
    # Run verification
    success = asyncio.run(verify_optimized_deployment())
    
    # Generate optimization report
    print("\n🔄 Generating optimization report...")
    reporter = SystemOptimizationReport()
    reporter.print_summary()
    
    # Final status
    if success:
        print("\n🎯 FINAL STATUS: DEPLOYMENT VERIFICATION SUCCESSFUL! 🎯")
        sys.exit(0)
    else:
        print("\n❌ FINAL STATUS: DEPLOYMENT VERIFICATION FAILED")
        sys.exit(1)

if __name__ == "__main__":
    main()