#!/usr/bin/env python3
"""
Comprehensive Model Training Launcher
Start training of all preferred models with GPU acceleration
"""

import asyncio
import subprocess
import sys
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

console = Console()

async def main():
    """Launch comprehensive training for all preferred models"""
    
    console.print(Panel(
        "[bold blue]🚀 Starting Comprehensive AI Model Training[/bold blue]\n\n"
        "Training Models:\n"
        "• DeepSeek R1 (Advanced Reasoning)\n"
        "• Mistral (Strategy Optimization)\n" 
        "• Gemma 3 12B (Market Analysis)\n"
        "• Phi 4 9B (Risk Assessment)\n"
        "• Qwen (General Intelligence)\n\n"
        "[yellow]This will utilize GPU acceleration and parallel training[/yellow]",
        title="Noryon AI Training Pipeline"
    ))
    
    # Training commands in order of preference
    training_commands = [
        {
            "name": "GPU Accelerated Training",
            "command": ["python", "gpu_accelerated_training_system.py", "--parallel", "--models", "deepseek,mistral,gemma,phi,qwen"],
            "description": "Primary training with GPU optimization"
        },
        {
            "name": "Advanced Parallel Training", 
            "command": ["python", "advanced_parallel_training_system.py", "--financial-focus"],
            "description": "Specialized financial training"
        },
        {
            "name": "Robust Training Fallback",
            "command": ["python", "train_models_robust.py", "--model", "all", "--quick"],
            "description": "Fallback training method"
        }
    ]
    
    for training_config in training_commands:
        try:
            console.print(f"\n[green]Starting: {training_config['name']}[/green]")
            console.print(f"Description: {training_config['description']}")
            
            # Execute training command
            result = subprocess.run(
                training_config["command"],
                capture_output=True,
                text=True,
                timeout=3600  # 1 hour timeout
            )
            
            if result.returncode == 0:
                console.print(f"[green]✅ {training_config['name']} completed successfully[/green]")
                break
            else:
                console.print(f"[yellow]⚠️ {training_config['name']} failed, trying next method...[/yellow]")
                console.print(f"Error: {result.stderr[:200]}...")
                
        except subprocess.TimeoutExpired:
            console.print(f"[yellow]⏰ {training_config['name']} timed out, trying next method...[/yellow]")
        except Exception as e:
            console.print(f"[red]❌ {training_config['name']} error: {e}[/red]")
    
    # Validate trained models
    console.print("\n[blue]🔍 Validating trained models...[/blue]")
    try:
        validation_result = subprocess.run(
            ["python", "train_all_models.py", "--validate-only"],
            capture_output=True,
            text=True,
            timeout=300
        )
        
        if validation_result.returncode == 0:
            console.print("[green]✅ Model validation completed[/green]")
        else:
            console.print("[yellow]⚠️ Some models may need retraining[/yellow]")
            
    except Exception as e:
        console.print(f"[red]❌ Validation error: {e}[/red]")
    
    # Next steps
    console.print(Panel(
        "[bold green]🎯 Training Complete - Next Steps:[/bold green]\n\n"
        "1. Test ensemble system: `python ensemble_voting_system.py --test-all`\n"
        "2. Start paper trading: `python start_paper_trading.py --ensemble-mode`\n"
        "3. Monitor performance: `python live_dashboard.py`\n"
        "4. Optimize system: `python performance_optimization_roadmap.py`",
        title="Next Actions"
    ))

if __name__ == "__main__":
    asyncio.run(main())
