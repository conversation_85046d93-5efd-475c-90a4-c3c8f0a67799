#!/usr/bin/env python3
"""
Production Monitoring System for Noryon AI Trading
Real-time monitoring of the 9-model ensemble in production
"""

import asyncio
import subprocess
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List
from dataclasses import dataclass
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.live import Live

console = Console()

@dataclass
class ModelHealth:
    name: str
    status: str
    response_time: float
    last_check: datetime
    error_count: int
    success_rate: float

class ProductionMonitor:
    """Real-time production monitoring for the 10-model ensemble"""
    
    def __init__(self):
        self.models = [
            "noryon-phi-4-9b-finance:latest",
            "noryon-gemma-3-12b-finance:latest",
            "noryon-phi-4-9b-enhanced-enhanced:latest",
            "noryon-gemma-3-12b-enhanced-enhanced:latest",
            "noryon-qwen3-finance-v2:latest",
            "noryon-cogito-finance-v2:latest",
            "noryon-marco-o1-finance-v2:latest",
            "noryon-deepscaler-finance-v2:latest",
            "noryon-granite-vision-finance-v1:latest",  # New visual analysis model
            "noryon-falcon3-finance-v1:latest"  # Fixed Falcon3 model - 10th model!
        ]
        
        self.health_data = {}
        self.alert_thresholds = {
            "response_time": 30.0,  # seconds
            "success_rate": 0.8,    # 80%
            "error_count": 5        # max errors per hour
        }
        
    async def check_model_health(self, model_name: str) -> ModelHealth:
        """Check individual model health"""
        start_time = time.time()
        
        try:
            result = subprocess.run([
                'ollama', 'run', model_name, 'Quick health check for AAPL at $185'
            ], capture_output=True, text=True, timeout=30)
            
            response_time = time.time() - start_time
            
            if result.returncode == 0:
                return ModelHealth(
                    name=model_name,
                    status="healthy",
                    response_time=response_time,
                    last_check=datetime.now(),
                    error_count=0,
                    success_rate=1.0
                )
            else:
                return ModelHealth(
                    name=model_name,
                    status="error",
                    response_time=response_time,
                    last_check=datetime.now(),
                    error_count=1,
                    success_rate=0.0
                )
                
        except subprocess.TimeoutExpired:
            return ModelHealth(
                name=model_name,
                status="timeout",
                response_time=30.0,
                last_check=datetime.now(),
                error_count=1,
                success_rate=0.0
            )
        except Exception as e:
            return ModelHealth(
                name=model_name,
                status="failed",
                response_time=0.0,
                last_check=datetime.now(),
                error_count=1,
                success_rate=0.0
            )
    
    def generate_monitoring_dashboard(self) -> Table:
        """Generate real-time monitoring dashboard"""
        table = Table(title="🚀 Noryon AI Production Monitor")
        table.add_column("Model", style="cyan")
        table.add_column("Status", style="green")
        table.add_column("Response Time", style="yellow")
        table.add_column("Success Rate", style="blue")
        table.add_column("Last Check", style="magenta")
        
        for model_name in self.models:
            if model_name in self.health_data:
                health = self.health_data[model_name]
                
                # Status with emoji
                if health.status == "healthy":
                    status = "✅ Healthy"
                elif health.status == "timeout":
                    status = "⏰ Timeout"
                elif health.status == "error":
                    status = "❌ Error"
                else:
                    status = "🔴 Failed"
                
                # Response time with color coding
                if health.response_time < 10:
                    response_time = f"🟢 {health.response_time:.1f}s"
                elif health.response_time < 20:
                    response_time = f"🟡 {health.response_time:.1f}s"
                else:
                    response_time = f"🔴 {health.response_time:.1f}s"
                
                # Success rate
                success_rate = f"{health.success_rate*100:.1f}%"
                
                # Last check
                last_check = health.last_check.strftime("%H:%M:%S")
                
                table.add_row(
                    model_name.split('-')[-1].split(':')[0],
                    status,
                    response_time,
                    success_rate,
                    last_check
                )
            else:
                table.add_row(
                    model_name.split('-')[-1].split(':')[0],
                    "⚪ Unknown",
                    "N/A",
                    "N/A",
                    "Never"
                )
        
        return table
    
    async def continuous_monitoring(self, duration_minutes: int = 60):
        """Run continuous monitoring for specified duration"""
        console.print(Panel(
            f"[bold blue]🚀 Starting Production Monitoring[/bold blue]\n\n"
            f"Monitoring {len(self.models)} models (including new Falcon3!) for {duration_minutes} minutes\n"
            f"Health checks every 30 seconds\n\n"
            f"Alert Thresholds:\n"
            f"• Response Time: >{self.alert_thresholds['response_time']}s\n"
            f"• Success Rate: <{self.alert_thresholds['success_rate']*100}%\n"
            f"• Error Count: >{self.alert_thresholds['error_count']}/hour",
            title="Production Monitor"
        ))
        
        end_time = datetime.now() + timedelta(minutes=duration_minutes)
        
        with Live(self.generate_monitoring_dashboard(), refresh_per_second=1) as live:
            while datetime.now() < end_time:
                # Check all models
                for model_name in self.models:
                    health = await self.check_model_health(model_name)
                    self.health_data[model_name] = health
                    
                    # Check for alerts
                    await self.check_alerts(health)
                
                # Update dashboard
                live.update(self.generate_monitoring_dashboard())
                
                # Wait before next check
                await asyncio.sleep(30)
        
        # Generate final report
        self.generate_monitoring_report()
    
    async def check_alerts(self, health: ModelHealth):
        """Check for alert conditions"""
        alerts = []
        
        if health.response_time > self.alert_thresholds['response_time']:
            alerts.append(f"High response time: {health.response_time:.1f}s")
        
        if health.success_rate < self.alert_thresholds['success_rate']:
            alerts.append(f"Low success rate: {health.success_rate*100:.1f}%")
        
        if health.status != "healthy":
            alerts.append(f"Model status: {health.status}")
        
        if alerts:
            console.print(f"[red]🚨 ALERT - {health.name}: {', '.join(alerts)}[/red]")
    
    def generate_monitoring_report(self):
        """Generate final monitoring report"""
        healthy_models = sum(1 for h in self.health_data.values() if h.status == "healthy")
        avg_response_time = sum(h.response_time for h in self.health_data.values()) / len(self.health_data)
        
        console.print(Panel(
            f"[bold green]📊 Monitoring Report[/bold green]\n\n"
            f"Healthy Models: {healthy_models}/{len(self.models)}\n"
            f"System Health: {healthy_models/len(self.models)*100:.1f}%\n"
            f"Average Response Time: {avg_response_time:.1f}s\n\n"
            f"🎯 Recommendations:\n"
            f"• {'✅ System performing well' if healthy_models >= 8 else '⚠️ Check failed models'}\n"
            f"• {'✅ Response times good' if avg_response_time < 15 else '⚠️ Optimize response times'}\n"
            f"• Continue monitoring for production stability",
            title="Final Report"
        ))

async def main():
    """Main monitoring function"""
    monitor = ProductionMonitor()
    
    console.print("[bold blue]🚀 Starting Noryon AI Production Monitoring...[/bold blue]\n")
    
    # Run monitoring for 1 hour (adjust as needed)
    await monitor.continuous_monitoring(duration_minutes=60)
    
    console.print("\n[bold green]✅ Monitoring session complete![/bold green]")

if __name__ == "__main__":
    asyncio.run(main())
