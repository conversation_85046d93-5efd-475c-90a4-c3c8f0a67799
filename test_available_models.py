#!/usr/bin/env python3
"""
Test Available AI Models
Check which models are available and working
"""

import subprocess
import sys
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

console = Console()

def test_ollama_models():
    """Test Ollama models"""
    try:
        result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')[1:]  # Skip header
            models = []
            for line in lines:
                if line.strip():
                    model_name = line.split()[0]
                    if 'finance' in model_name.lower():
                        models.append(model_name)
            return models
    except:
        pass
    return []

def test_ollama_model_response(model_name):
    """Test if an Ollama model can respond"""
    try:
        result = subprocess.run([
            'ollama', 'run', model_name, 
            'What is the current trend in the stock market? Give a brief analysis.'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0 and result.stdout.strip():
            return True, result.stdout[:200] + "..."
        else:
            return False, "No response"
    except subprocess.TimeoutExpired:
        return False, "Timeout"
    except Exception as e:
        return False, str(e)

def check_trained_models():
    """Check for trained HuggingFace models"""
    models_dir = Path("models")
    trained_models = []
    
    if models_dir.exists():
        for model_dir in models_dir.iterdir():
            if model_dir.is_dir():
                # Check for model files
                has_model = (
                    any(model_dir.glob("*.bin")) or 
                    any(model_dir.glob("*.safetensors")) or
                    any(model_dir.glob("pytorch_model.bin")) or
                    any(model_dir.glob("model.safetensors"))
                )
                has_config = (model_dir / "config.json").exists()
                
                if has_model and has_config:
                    trained_models.append({
                        "name": model_dir.name,
                        "path": str(model_dir),
                        "complete": True
                    })
                elif model_dir.name.endswith(('-finance', '-trained', '-clean')):
                    trained_models.append({
                        "name": model_dir.name,
                        "path": str(model_dir),
                        "complete": False
                    })
    
    return trained_models

def main():
    """Test all available models"""
    console.print(Panel(
        "[bold blue]AI Model Availability Test[/bold blue]\n\n"
        "Checking which AI models are available and working",
        title="Model Test"
    ))
    
    # Test Ollama models
    console.print("\n[yellow]Testing Ollama Models...[/yellow]")
    ollama_models = test_ollama_models()
    
    if ollama_models:
        ollama_table = Table(title="Available Ollama Finance Models")
        ollama_table.add_column("Model Name", style="cyan")
        ollama_table.add_column("Status", style="green")
        ollama_table.add_column("Response Test", style="yellow")
        
        for model in ollama_models[:5]:  # Test first 5 models
            console.print(f"Testing {model}...")
            working, response = test_ollama_model_response(model)
            status = "✅ Working" if working else "❌ Failed"
            ollama_table.add_row(model, status, response[:50] + "..." if len(response) > 50 else response)
        
        console.print(ollama_table)
        console.print(f"[green]Found {len(ollama_models)} Ollama finance models[/green]")
    else:
        console.print("[red]No Ollama models found[/red]")
    
    # Test trained models
    console.print("\n[yellow]Checking Trained Models...[/yellow]")
    trained_models = check_trained_models()
    
    if trained_models:
        trained_table = Table(title="Trained HuggingFace Models")
        trained_table.add_column("Model Name", style="cyan")
        trained_table.add_column("Status", style="green")
        trained_table.add_column("Path", style="yellow")
        
        for model in trained_models:
            status = "✅ Complete" if model["complete"] else "⚠️ Incomplete"
            trained_table.add_row(model["name"], status, model["path"])
        
        console.print(trained_table)
        console.print(f"[green]Found {len(trained_models)} trained models[/green]")
    else:
        console.print("[red]No trained models found[/red]")
    
    # Summary and recommendations
    total_working = len([m for m in ollama_models if m]) + len([m for m in trained_models if m["complete"]])
    
    console.print(Panel(
        f"[bold green]Model Availability Summary[/bold green]\n\n"
        f"Ollama Models: {len(ollama_models)}\n"
        f"Trained Models: {len([m for m in trained_models if m['complete']])}\n"
        f"Total Working Models: {total_working}\n\n"
        f"Recommendations:\n"
        f"{'• Start trading with Ollama models' if ollama_models else '• Train more models'}\n"
        f"{'• Test ensemble system' if total_working > 3 else '• Continue training'}\n"
        f"{'• Launch paper trading' if total_working > 0 else '• Focus on model training'}",
        title="Summary"
    ))
    
    # Next steps
    if total_working > 0:
        console.print(Panel(
            "[bold yellow]Ready for Trading![/bold yellow]\n\n"
            "You have working AI models. Next steps:\n\n"
            "1. Test ensemble system:\n"
            "   python ensemble_voting_system.py --test-all\n\n"
            "2. Start paper trading:\n"
            "   python start_paper_trading.py --quick-start\n\n"
            "3. Monitor performance:\n"
            "   python live_dashboard.py",
            title="Next Steps"
        ))
    else:
        console.print(Panel(
            "[bold red]No Working Models[/bold red]\n\n"
            "Continue training or check model setup:\n\n"
            "1. Continue current training\n"
            "2. Try alternative training methods\n"
            "3. Check Ollama installation",
            title="Action Required"
        ))

if __name__ == "__main__":
    main()
