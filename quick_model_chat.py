#!/usr/bin/env python3
"""
Quick Model Chat - Interactive testing of AI models
"""

import subprocess
import time
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.prompt import Prompt

console = Console()

class QuickModelChat:
    """Quick interactive chat with AI models"""
    
    def __init__(self):
        self.available_models = []
        self.get_models()
    
    def get_models(self):
        """Get available models"""
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]  # Skip header
                for line in lines:
                    if line.strip():
                        parts = line.split()
                        if len(parts) >= 1:
                            self.available_models.append(parts[0])
        except Exception as e:
            console.print(f"[red]Error getting models: {e}[/red]")
    
    def display_models(self):
        """Display available models"""
        console.print(Panel(
            "[bold blue]🤖 AVAILABLE AI MODELS[/bold blue]\n\n"
            "Choose a model to chat with for testing",
            title="Model Selection"
        ))
        
        # Group models by type
        finance_models = [m for m in self.available_models if 'finance' in m.lower()]
        enhanced_models = [m for m in self.available_models if 'enhanced' in m.lower()]
        reasoning_models = [m for m in self.available_models if 'reasoning' in m.lower()]
        regular_models = [m for m in self.available_models if m not in finance_models + enhanced_models + reasoning_models]
        
        model_table = Table(title="🎯 Model Categories")
        model_table.add_column("Category", style="cyan", width=20)
        model_table.add_column("Models", style="green", width=60)
        
        if finance_models:
            model_table.add_row("🏦 Finance Specialists", ", ".join(finance_models[:3]) + f" (+{len(finance_models)-3} more)" if len(finance_models) > 3 else ", ".join(finance_models))
        
        if reasoning_models:
            model_table.add_row("🧠 Reasoning Models", ", ".join(reasoning_models[:2]) + f" (+{len(reasoning_models)-2} more)" if len(reasoning_models) > 2 else ", ".join(reasoning_models))
        
        if enhanced_models:
            model_table.add_row("⚡ Enhanced Models", ", ".join(enhanced_models[:2]) + f" (+{len(enhanced_models)-2} more)" if len(enhanced_models) > 2 else ", ".join(enhanced_models))
        
        if regular_models:
            model_table.add_row("📦 Regular Models", ", ".join(regular_models[:3]) + f" (+{len(regular_models)-3} more)" if len(regular_models) > 3 else ", ".join(regular_models))
        
        console.print(model_table)
        
        console.print(f"\n[yellow]Total Models Available: {len(self.available_models)}[/yellow]")
    
    def quick_test_models(self):
        """Quick test of top models"""
        console.print(Panel(
            "[bold blue]⚡ QUICK MODEL TESTING[/bold blue]\n\n"
            "Testing top models with a simple question",
            title="Quick Test"
        ))
        
        # Select top models for quick testing
        test_models = []
        
        # Add best finance models
        finance_models = [m for m in self.available_models if 'finance' in m.lower()]
        if finance_models:
            test_models.extend(finance_models[:3])
        
        # Add reasoning models
        reasoning_models = [m for m in self.available_models if 'reasoning' in m.lower()]
        if reasoning_models:
            test_models.extend(reasoning_models[:2])
        
        # Add enhanced models
        enhanced_models = [m for m in self.available_models if 'enhanced' in m.lower()]
        if enhanced_models:
            test_models.extend(enhanced_models[:2])
        
        test_question = "What is your specialty and how can you help with trading?"
        
        results_table = Table(title="🧪 Quick Test Results")
        results_table.add_column("Model", style="cyan", width=30)
        results_table.add_column("Response", style="green", width=50)
        results_table.add_column("Status", style="yellow", width=10)
        
        for model in test_models[:5]:  # Test top 5 models
            console.print(f"[blue]Testing {model}...[/blue]")
            
            try:
                result = subprocess.run(
                    ['ollama', 'run', model, test_question], 
                    capture_output=True, text=True, timeout=30
                )
                
                if result.returncode == 0:
                    response = result.stdout.strip()
                    short_response = response[:100] + "..." if len(response) > 100 else response
                    results_table.add_row(model, short_response, "✅ Working")
                else:
                    results_table.add_row(model, "Error occurred", "❌ Failed")
                    
            except subprocess.TimeoutExpired:
                results_table.add_row(model, "Response timeout", "⏱️ Slow")
            except Exception as e:
                results_table.add_row(model, f"Error: {str(e)[:50]}", "❌ Error")
        
        console.print(results_table)
    
    def interactive_chat(self):
        """Start interactive chat with selected model"""
        console.print(Panel(
            "[bold blue]💬 INTERACTIVE MODEL CHAT[/bold blue]\n\n"
            "Choose a model to start chatting",
            title="Interactive Chat"
        ))
        
        # Show recommended models
        recommended = [
            "noryon-phi4-reasoning-finance-v2",
            "noryon-gemma-3-12b-finance", 
            "noryon-deepseek-r1-finance-v2",
            "noryon-qwen3-finance-v2"
        ]
        
        available_recommended = [m for m in recommended if m in self.available_models]
        
        if available_recommended:
            console.print("[green]🎯 Recommended Models:[/green]")
            for i, model in enumerate(available_recommended, 1):
                console.print(f"  {i}. {model}")
            
            console.print(f"\n[blue]Or type any model name from the {len(self.available_models)} available models[/blue]")
            
            choice = Prompt.ask("Choose model (number or name)", default="1")
            
            if choice.isdigit() and 1 <= int(choice) <= len(available_recommended):
                selected_model = available_recommended[int(choice) - 1]
            else:
                selected_model = choice
            
            if selected_model in self.available_models:
                self.start_chat_session(selected_model)
            else:
                console.print(f"[red]Model '{selected_model}' not found[/red]")
        else:
            console.print("[yellow]No recommended models found. Showing all available models:[/yellow]")
            for i, model in enumerate(self.available_models[:10], 1):
                console.print(f"  {i}. {model}")
            
            choice = Prompt.ask("Choose model number", default="1")
            if choice.isdigit() and 1 <= int(choice) <= len(self.available_models):
                selected_model = self.available_models[int(choice) - 1]
                self.start_chat_session(selected_model)
    
    def start_chat_session(self, model_name):
        """Start chat session with specific model"""
        console.print(Panel(
            f"[bold green]🤖 CHATTING WITH: {model_name}[/bold green]\n\n"
            "Type your questions. Type 'quit' to exit.",
            title="Chat Session"
        ))
        
        # Start the model in interactive mode
        try:
            process = subprocess.Popen(
                ['ollama', 'run', model_name],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1
            )
            
            console.print(f"[green]✅ {model_name} is ready! Start chatting...[/green]")
            console.print("[yellow]Type 'quit' to exit the chat[/yellow]\n")
            
            while True:
                user_input = Prompt.ask("[bold blue]You[/bold blue]")
                
                if user_input.lower() in ['quit', 'exit', 'bye']:
                    break
                
                # Send input to model
                process.stdin.write(user_input + '\n')
                process.stdin.flush()
                
                console.print(f"[green]{model_name}[/green]: Thinking...")
                
                # Note: This is a simplified version. In practice, you'd need more complex
                # handling to read the model's response properly
                time.sleep(2)
                console.print("[yellow]Note: For full interactive chat, use: ollama run [model_name][/yellow]")
                break
            
            process.terminate()
            
        except Exception as e:
            console.print(f"[red]Error starting chat: {e}[/red]")
            console.print(f"[yellow]Try manually: ollama run {model_name}[/yellow]")
    
    def show_model_commands(self):
        """Show commands to manually test models"""
        console.print(Panel(
            "[bold blue]🔧 MANUAL MODEL TESTING COMMANDS[/bold blue]\n\n"
            "Use these commands to test models manually",
            title="Manual Testing"
        ))
        
        # Show top models with commands
        top_models = [
            "noryon-phi4-reasoning-finance-v2",
            "noryon-gemma-3-12b-finance",
            "noryon-deepseek-r1-finance-v2", 
            "noryon-qwen3-finance-v2",
            "noryon-falcon3-finance-v1"
        ]
        
        available_top = [m for m in top_models if m in self.available_models]
        
        console.print("[green]🎯 Top Finance Models - Copy & Paste Commands:[/green]\n")
        
        for model in available_top:
            console.print(f"[cyan]# Test {model}[/cyan]")
            console.print(f"[yellow]ollama run {model}[/yellow]")
            console.print()
        
        console.print("[blue]💡 Sample Questions to Ask:[/blue]")
        questions = [
            "What is your specialty?",
            "Should I buy AAPL stock?",
            "Analyze Bitcoin vs Ethereum",
            "What's the risk of a tech portfolio?",
            "Explain options trading"
        ]
        
        for q in questions:
            console.print(f"  • {q}")

def main():
    """Main function"""
    console.print(Panel(
        "[bold blue]🧪 QUICK AI MODEL TESTING[/bold blue]\n\n"
        "Interactive testing of your 26 AI models",
        title="Model Testing"
    ))
    
    tester = QuickModelChat()
    
    if not tester.available_models:
        console.print("[red]❌ No models found. Make sure Ollama is running.[/red]")
        return
    
    while True:
        console.print("\n[bold yellow]Choose an option:[/bold yellow]")
        console.print("1. 📋 Show all available models")
        console.print("2. ⚡ Quick test top models")
        console.print("3. 💬 Interactive chat with a model")
        console.print("4. 🔧 Show manual testing commands")
        console.print("5. 🚪 Exit")
        
        choice = Prompt.ask("Your choice", choices=["1", "2", "3", "4", "5"], default="2")
        
        if choice == "1":
            tester.display_models()
        elif choice == "2":
            tester.quick_test_models()
        elif choice == "3":
            tester.interactive_chat()
        elif choice == "4":
            tester.show_model_commands()
        elif choice == "5":
            console.print("[green]👋 Happy trading with your AI models![/green]")
            break

if __name__ == "__main__":
    main()
